{"Version": 1, "TypeId": "NetworkProjectConfig", "PeerMode": 0, "LagCompensation": {"Enabled": true, "HitboxBufferLengthInMs": 200, "HitboxDefaultCapacity": 512, "CachedStaticCollidersSize": 64}, "EnqueueIncompleteSynchronousSpawns": true, "InvokeRenderInBatchMode": false, "NetworkIdIsObjectName": false, "HideNetworkObjectInactivityGuard": false, "AllowClientServerModesInWebGL": false, "ClientsRecordFrameAndPacketTimingTraces": false, "Simulation": {"InputDataWordCount": 0, "ReplicationFeatures": 1, "InputTransferMode": 0, "SimulationUpdateTimeMode": 0, "PlayerCount": 255, "TickRateSelection": {"Client": 100, "ServerIndex": 0, "ClientSendIndex": 0, "ServerSendIndex": 0}}, "Network": {"ConnectionTimeout": 10.0, "ConnectionShutdownTime": 1.0, "ReliableDataTransferModes": 3}, "HostMigration": {"EnableAutoUpdate": false, "UpdateDelay": 10}, "EncryptionConfig": {"EnableEncryption": false}, "NetworkConditions": {"Enabled": false, "DelayShape": 0, "DelayMin": 0.15, "DelayMax": 0.15, "DelayPeriod": 0.0, "DelayThreshold": 0.0, "AdditionalJitter": 0.05, "LossChanceShape": 0, "LossChanceMin": 0.05, "LossChanceMax": 0.05, "LossChanceThreshold": 0.0, "LossChancePeriod": 0.0, "AdditionalLoss": 0.0}, "Heap": {"PageShift": 15, "PageCount": 256, "GlobalsSize": 0}, "AssembliesToWeave": ["Fusion.Unity", "Assembly-CSharp", "Assembly-CSharp-firstpass", "Fusion.Addons.Physics", "Fusion.Addons.FSM", "D<PERSON>.Sc<PERSON>.Runtime", "PhotonVoice.Fusion"], "UseSerializableDictionary": true, "NullChecksForNetworkedProperties": true, "CheckRpcAttributeUsage": false, "CheckNetworkedPropertiesBeingEmpty": true}